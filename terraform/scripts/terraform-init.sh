#!/usr/bin/env sh
set -e

# Terraform initialization script for GitLab CI/CD
# This script configures Terraform backend for GitLab state management
# Uses environment variables for authentication to avoid token persistence in plan files

ENVIRONMENT=${1:-development}
PROJECT_ID=${CI_PROJECT_ID}
CI_JOB_TOKEN=${CI_JOB_TOKEN}
GITLAB_URL=${CI_SERVER_URL}

if [ -z "$PROJECT_ID" ] || [ -z "$CI_JOB_TOKEN" ] || [ -z "$GITLAB_URL" ]; then
    echo "❌ Error: Required GitLab CI variables are missing"
    echo "   PROJECT_ID: $PROJECT_ID"
    echo "   CI_JOB_TOKEN: [${CI_JOB_TOKEN:+SET}${CI_JOB_TOKEN:-MISSING}]"
    echo "   GITLAB_URL: $GITLAB_URL"
    exit 1
fi

echo "🔧 Initializing Terraform for environment: $ENVIRONMENT"
echo "📍 Project ID: $PROJECT_ID"
echo "🌐 GitLab URL: $GITLAB_URL"
echo "🔑 Using CI_JOB_TOKEN for authentication"

# Navigate to environment directory
cd "terraform/environments/$ENVIRONMENT"

# Set Terraform HTTP backend environment variables for authentication only
# This approach avoids embedding tokens in plan files while providing required addresses
export TF_HTTP_USERNAME="gitlab-ci-token"
export TF_HTTP_PASSWORD="${CI_JOB_TOKEN}"

# Build backend addresses
BACKEND_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}"
LOCK_ADDRESS="${BACKEND_ADDRESS}/lock"

echo "🔧 Backend configuration:"
echo "   Address: $BACKEND_ADDRESS"
echo "   Lock Address: $LOCK_ADDRESS"
echo "   Username: $TF_HTTP_USERNAME"
echo "   Password: [REDACTED]"

# Initialize Terraform with GitLab backend
# Use -backend-config for addresses (non-sensitive) and environment variables for auth (sensitive)
# This prevents token embedding in plan files while providing required configuration
terraform init \
    -backend-config="address=${BACKEND_ADDRESS}" \
    -backend-config="lock_address=${LOCK_ADDRESS}" \
    -backend-config="unlock_address=${LOCK_ADDRESS}" \
    -backend-config="lock_method=POST" \
    -backend-config="unlock_method=DELETE" \
    -backend-config="retry_wait_min=5"

echo "✅ Terraform initialized successfully for $ENVIRONMENT environment"
