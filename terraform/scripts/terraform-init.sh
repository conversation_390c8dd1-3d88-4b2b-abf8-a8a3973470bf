#!/usr/bin/env sh
set -e

# Terraform initialization script for GitLab CI/CD
# This script configures Terraform backend for GitLab state management
# Uses environment variables for authentication to avoid token persistence in plan files

ENVIRONMENT=${1:-development}
PROJECT_ID=${CI_PROJECT_ID}
CI_JOB_TOKEN=${CI_JOB_TOKEN}
GITLAB_URL=${CI_SERVER_URL}

if [ -z "$PROJECT_ID" ] || [ -z "$CI_JOB_TOKEN" ] || [ -z "$GITLAB_URL" ]; then
    echo "❌ Error: Required GitLab CI variables are missing"
    echo "   PROJECT_ID: $PROJECT_ID"
    echo "   CI_JOB_TOKEN: [${CI_JOB_TOKEN:+SET}${CI_JOB_TOKEN:-MISSING}]"
    echo "   GITLAB_URL: $GITLAB_URL"
    exit 1
fi

echo "🔧 Initializing Terraform for environment: $ENVIRONMENT"
echo "📍 Project ID: $PROJECT_ID"
echo "🌐 GitLab URL: $GITLAB_URL"
echo "🔑 Using CI_JOB_TOKEN for authentication"

# Navigate to environment directory
cd "terraform/environments/$ENVIRONMENT"

# Set Terraform HTTP backend environment variables
export TF_HTTP_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}"
export TF_HTTP_LOCK_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}/lock"
export TF_HTTP_UNLOCK_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}/lock"
export TF_HTTP_LOCK_METHOD="POST"
export TF_HTTP_UNLOCK_METHOD="DELETE"
export TF_HTTP_RETRY_WAIT_MIN="5"
export TF_HTTP_USERNAME="gitlab-ci-token"
export TF_HTTP_PASSWORD="${CI_JOB_TOKEN}"

echo "🔧 Backend configuration:"
echo "   Address: $TF_HTTP_ADDRESS"

# Initialize Terraform with GitLab backend using environment variables
# No -backend-config parameters needed - all configuration via environment variables
terraform init

echo "✅ Terraform initialized successfully for $ENVIRONMENT environment"
