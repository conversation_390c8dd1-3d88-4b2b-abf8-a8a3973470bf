#!/usr/bin/env sh
set -e

# Terraform initialization script for GitLab CI/CD
# This script configures Terraform backend for GitLab state management
# Uses environment variables for authentication to avoid token persistence in plan files

ENVIRONMENT=${1:-development}
PROJECT_ID=${CI_PROJECT_ID}
GITLAB_URL=${CI_SERVER_URL}

# Determine which token to use for authentication
# Prefer GL_TF_TOKEN over CI_JOB_TOKEN for better compatibility with plan/apply workflow
if [ -n "$GL_TF_TOKEN" ]; then
    AUTH_TOKEN="$GL_TF_TOKEN"
    AUTH_USERNAME="gitlab-ci-token"
    echo "🔑 Using GL_TF_TOKEN for authentication"
elif [ -n "$CI_JOB_TOKEN" ]; then
    AUTH_TOKEN="$CI_JOB_TOKEN"
    AUTH_USERNAME="gitlab-ci-token"
    echo "🔑 Using CI_JOB_TOKEN for authentication"
else
    echo "❌ Error: No authentication token available"
    echo "   Please set either GL_TF_TOKEN or CI_JOB_TOKEN"
    echo "   GL_TF_TOKEN is preferred for Terraform state operations"
    exit 1
fi

if [ -z "$PROJECT_ID" ] || [ -z "$GITLAB_URL" ]; then
    echo "❌ Error: Required GitLab CI variables are missing"
    echo "   PROJECT_ID: $PROJECT_ID"
    echo "   GITLAB_URL: $GITLAB_URL"
    exit 1
fi

echo "🔧 Initializing Terraform for environment: $ENVIRONMENT"
echo "📍 Project ID: $PROJECT_ID"
echo "🌐 GitLab URL: $GITLAB_URL"

# Navigate to environment directory
cd "terraform/environments/$ENVIRONMENT"

# Set Terraform HTTP backend environment variables
# This approach avoids embedding tokens in plan files
export TF_HTTP_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}"
export TF_HTTP_LOCK_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}/lock"
export TF_HTTP_UNLOCK_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}/lock"
export TF_HTTP_LOCK_METHOD="POST"
export TF_HTTP_UNLOCK_METHOD="DELETE"
export TF_HTTP_RETRY_WAIT_MIN="5"
export TF_HTTP_USERNAME="$AUTH_USERNAME"
export TF_HTTP_PASSWORD="$AUTH_TOKEN"

echo "🔧 Backend configuration:"
echo "   Address: $TF_HTTP_ADDRESS"
echo "   Username: $TF_HTTP_USERNAME"
echo "   Password: [REDACTED]"

# Initialize Terraform with GitLab backend using environment variables
terraform init

echo "✅ Terraform initialized successfully for $ENVIRONMENT environment"
