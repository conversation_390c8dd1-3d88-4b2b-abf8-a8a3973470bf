image: docker:24           # default for jobs that build/push images

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  CONTAINER_IMAGE: $CI_REGISTRY_IMAGE
  DEV_LATEST_TAG: dev-latest
  # Terraform configuration
  TF_ROOT: ${CI_PROJECT_DIR}/terraform
  TF_ADDRESS: ${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/terraform/state

stages:
  - version
  - build
  - plan
  - deploy

#####################################################################
# YAML ANCHORS - REUSABLE CONFIGURATION PATTERNS
#####################################################################

# Common semantic-release setup for version jobs
.semantic_release_setup: &semantic_release_setup
  - apk add --no-cache git
  # Validate required variables for semantic-release
  - |
    echo "🔍 Validating semantic-release variables..."
    if [ -z "$GL_TOKEN" ]; then
      echo "❌ Error: GL_TOKEN not configured"
      echo "   Please set GL_TOKEN in GitLab CI/CD variables"
      echo "   Go to: Settings > CI/CD > Variables"
      echo "   Create a GitLab access token with 'api' scope"
      exit 1
    fi
    if [ -z "$CI_SERVER_HOST" ] || [ -z "$CI_PROJECT_PATH" ]; then
      echo "❌ Error: Required GitLab CI built-in variables missing"
      echo "   CI_SERVER_HOST: ${CI_SERVER_HOST:-MISSING}"
      echo "   CI_PROJECT_PATH: ${CI_PROJECT_PATH:-MISSING}"
      exit 1
    fi
    echo "✅ Semantic-release variables validated"
  # authenticated remote so semantic-release can push tags and commits
  - git remote set-url origin https://gitlab-ci-token:${GL_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
  - git config user.email "ci-pipeline@${CI_SERVER_HOST}"
  - git config user.name  "ci-pipeline"
  # ensure we're on the correct branch and have latest commits
  - git fetch origin "$CI_COMMIT_REF_NAME"
  - git checkout "$CI_COMMIT_REF_NAME"
  - git reset --hard "origin/$CI_COMMIT_REF_NAME"
  # fetch all tags to avoid conflicts
  - git fetch --tags
  # install semantic-release + plugins
  - npm install --no-save semantic-release @semantic-release/commit-analyzer @semantic-release/release-notes-generator @semantic-release/gitlab

# Common Terraform setup for infrastructure jobs
.terraform_setup: &terraform_setup
  # Validate required environment variables and verify Terraform installation
  - |
    echo "🔍 Validating required CI/CD variables..."

    # Check AWS credentials
    if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
      echo "❌ Error: AWS credentials not configured"
      echo "   Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in GitLab CI/CD variables"
      echo "   Go to: Settings > CI/CD > Variables"
      exit 1
    fi

    # Check AWS region (use default if not set)
    if [ -z "$AWS_DEFAULT_REGION" ]; then
      export AWS_DEFAULT_REGION="us-east-1"
      echo "⚠️  AWS_DEFAULT_REGION not set, using default: us-east-1"
    fi

    # Check GitLab registry credentials
    if [ -z "$CI_REGISTRY_USER" ] || [ -z "$CI_REGISTRY_PASSWORD" ]; then
      echo "❌ Error: GitLab registry credentials not available"
      echo "   CI_REGISTRY_USER and CI_REGISTRY_PASSWORD should be automatically provided by GitLab"
      echo "   Check GitLab registry settings and permissions"
      exit 1
    fi

    # Check GitLab CI built-in variables
    if [ -z "$CI_PROJECT_ID" ] || [ -z "$CI_SERVER_URL" ]; then
      echo "❌ Error: Required GitLab CI built-in variables are missing"
      echo "   CI_PROJECT_ID: ${CI_PROJECT_ID:-MISSING}"
      echo "   CI_SERVER_URL: ${CI_SERVER_URL:-MISSING}"
      exit 1
    fi

    # Check Terraform authentication tokens
    if [ -n "$GL_TF_TOKEN" ]; then
      echo "✅ GL_TF_TOKEN configured for Terraform state operations"
    elif [ -n "$CI_JOB_TOKEN" ]; then
      echo "⚠️  Using CI_JOB_TOKEN for Terraform state operations"
      echo "   Consider setting GL_TF_TOKEN for better plan/apply workflow compatibility"
    else
      echo "❌ Error: No authentication token available for Terraform state operations"
      echo "   Please set GL_TF_TOKEN in GitLab CI/CD variables"
      echo "   Go to: Settings > CI/CD > Variables"
      echo "   Create a GitLab access token with 'api' scope"
      exit 1
    fi

    echo "✅ All required variables validated"
  - |
    echo "🔍 Verifying Terraform installation..."

    # Display environment information
    echo "Container OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
    echo "Architecture: $(uname -m)"
    echo "Current user: $(whoami)"

    # Verify Terraform is available and working
    echo ""
    echo "Terraform version:"
    terraform --version
    echo ""
    echo "✅ Terraform verified and ready to use"
    echo "✅ AWS credentials configured for region: $AWS_DEFAULT_REGION"
    echo "✅ Ready to execute Terraform commands"

#####################################################################
# 1.  FEATURE BRANCH TESTING (MANUAL TRIGGER)
#####################################################################

test_on_development:
  stage: build
  image: docker:24
  services: [ docker:24-dind ]
  rules:
    # Only trigger for merge requests from feature branches (not development branch)
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != "development"'
      when: manual
  variables:
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
  script: |
    echo "🔧 Building feature branch image: $IMAGE_TAG"
    docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
    docker build -t "$CONTAINER_IMAGE:$IMAGE_TAG" .
    docker push "$CONTAINER_IMAGE:$IMAGE_TAG"

    echo "IMAGE_TAG=$IMAGE_TAG" > feature.env
  artifacts:
    reports:
      dotenv: feature.env

#-------------------------------------------------------------------#

terraform_plan_feature:
  stage: plan
  image:
    name: hashicorp/terraform:latest
    entrypoint: [ "/bin/sh", "-c" ]
  tags: [ docker ]
  needs: [ test_on_development ]
  rules:
    # Only trigger for merge requests from feature branches (not development branch)
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != "development"'
      when: manual
  before_script: *terraform_setup
  script: |
    echo "🔍 Planning Terraform changes for feature branch $IMAGE_TAG..."

    # Initialize Terraform for development environment
    sh ./terraform/scripts/terraform-init.sh development

    # Plan infrastructure changes
    cd terraform/environments/development
    terraform plan \
      -var="container_image=$CONTAINER_IMAGE:$IMAGE_TAG" \
      -var="gitlab_registry_user=$CI_REGISTRY_USER" \
      -var="gitlab_registry_password=$CI_REGISTRY_PASSWORD" \
      -out=tfplan

    echo "✅ Terraform plan completed for feature branch"
  artifacts:
    paths:
      - terraform/environments/development/tfplan
    expire_in: 1 hour

deploy_feature_to_dev:
  stage: deploy
  image:
    name: hashicorp/terraform:latest
    entrypoint: [ "/bin/sh", "-c" ]
  tags: [ docker ]
  needs: [ terraform_plan_feature ]
  rules:
    # Only trigger for merge requests from feature branches (not development branch)
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != "development"'
      when: manual
  environment:
    name: development
    url: https://dev.example.com
    action: prepare
  before_script: *terraform_setup
  script: |
    echo "🚀 Deploying feature branch $IMAGE_TAG to development environment..."

    # Initialize Terraform for development environment
    sh ./terraform/scripts/terraform-init.sh development

    # Apply infrastructure changes
    cd terraform/environments/development
    terraform apply -auto-approve tfplan

    # Get application URL
    APP_URL=$(terraform output -raw application_url)
    echo "📍 Application URL: $APP_URL"
    echo "🔍 Deployed Image: $CONTAINER_IMAGE:$IMAGE_TAG"
    echo "🔑 GitLab Registry Auth: $CI_REGISTRY_USER (password configured)"

    echo "✅ Feature deployment completed"

#####################################################################
# 2.  AUTOMATIC DEVELOPMENT PIPELINE
#####################################################################

release_dev:
  stage: version
  image: node:20-alpine
  tags: [ docker ]
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'
  variables:
    GIT_DEPTH: "0"                       # full history for semantic-release
  before_script: *semantic_release_setup
  script: |
    # run semantic-release (uses .releaserc.json configuration)
    npx semantic-release
    # fallback when no tags existed yet
    VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0-${CI_COMMIT_SHORT_SHA}")

    echo "RELEASE_VERSION=$VERSION" > release.env
  artifacts:
    reports:
      dotenv: release.env                # exported to all jobs that need this one

#-------------------------------------------------------------------#

build_dev:
  stage: build
  image: docker:24
  services: [ docker:24-dind ]
  needs: [ release_dev ]
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'
  script: |
    if [ -z "$RELEASE_VERSION" ]; then
      echo "❌  RELEASE_VERSION missing – release_dev failed?"
      exit 1
    fi

    docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
    docker build -t "$CONTAINER_IMAGE:$RELEASE_VERSION" -t "$CONTAINER_IMAGE:$DEV_LATEST_TAG" .
    docker push "$CONTAINER_IMAGE:$RELEASE_VERSION"
    docker push "$CONTAINER_IMAGE:$DEV_LATEST_TAG"

    echo "IMAGE_TAG=$RELEASE_VERSION" > build.env
  artifacts:
    reports:
      dotenv: build.env

#-------------------------------------------------------------------#

terraform_plan_dev:
  stage: plan
  image:
    name: hashicorp/terraform:latest
    entrypoint: [ "/bin/sh", "-c" ]
  tags: [ docker ]
  needs: [ build_dev ]
  rules:
    # Automatic plan when commits are pushed to development branch
    - if: '$CI_COMMIT_BRANCH == "development"'
      when: on_success
  before_script: *terraform_setup
  script: |
    # Debug variable resolution
    echo "🔍 Debug: IMAGE_TAG variable = '$IMAGE_TAG'"
    echo "🔍 Debug: DEV_LATEST_TAG variable = '$DEV_LATEST_TAG'"

    # Set deployment image tag with proper fallback
    if [ -n "$IMAGE_TAG" ]; then
      DEPLOY_IMAGE_TAG="$IMAGE_TAG"
      echo "✅ Using IMAGE_TAG: $DEPLOY_IMAGE_TAG"
    else
      DEPLOY_IMAGE_TAG="$DEV_LATEST_TAG"
      echo "✅ Using DEV_LATEST_TAG fallback: $DEPLOY_IMAGE_TAG"
    fi

    echo "🔍 Planning Terraform changes for development..."

    # Initialize Terraform for development environment
    sh ./terraform/scripts/terraform-init.sh development

    # Plan infrastructure changes
    cd terraform/environments/development
    terraform plan \
      -var="container_image=$CONTAINER_IMAGE:$DEPLOY_IMAGE_TAG" \
      -var="gitlab_registry_user=$CI_REGISTRY_USER" \
      -var="gitlab_registry_password=$CI_REGISTRY_PASSWORD" \
      -out=tfplan

    echo "✅ Terraform plan completed for development"
  artifacts:
    paths:
      - terraform/environments/development/tfplan
    expire_in: 1 hour

deploy_dev:
  stage: deploy
  image:
    name: hashicorp/terraform:latest
    entrypoint: [ "/bin/sh", "-c" ]
  tags: [ docker ]
  needs: [ terraform_plan_dev ]
  rules:
    # Automatic deployment when commits are pushed to development branch
    - if: '$CI_COMMIT_BRANCH == "development"'
      when: on_success
  environment:
    name: development
    url: https://dev.example.com
    action: prepare
  before_script: *terraform_setup
  script: |
    # Debug variable resolution
    echo "🔍 Debug: IMAGE_TAG variable = '$IMAGE_TAG'"
    echo "🔍 Debug: DEV_LATEST_TAG variable = '$DEV_LATEST_TAG'"

    # Set deployment image tag with proper fallback
    if [ -n "$IMAGE_TAG" ]; then
      DEPLOY_IMAGE_TAG="$IMAGE_TAG"
      echo "✅ Using IMAGE_TAG: $DEPLOY_IMAGE_TAG"
    else
      DEPLOY_IMAGE_TAG="$DEV_LATEST_TAG"
      echo "✅ Using DEV_LATEST_TAG fallback: $DEPLOY_IMAGE_TAG"
    fi

    echo "🚀 Deploying $DEPLOY_IMAGE_TAG to development environment..."

    # Initialize Terraform for development environment
    sh ./terraform/scripts/terraform-init.sh development

    # Apply infrastructure changes
    cd terraform/environments/development
    terraform apply -auto-approve tfplan

    # Get application URL and deployment info
    APP_URL=$(terraform output -raw application_url)
    DEPLOYED_IMAGE=$(terraform output -raw deployed_image)

    echo "📍 Application URL: $APP_URL"
    echo "🔍 Deployed Image: $DEPLOYED_IMAGE"
    echo "🔑 GitLab Registry Auth: $CI_REGISTRY_USER (password configured)"
    echo "✅ Development deployment completed"
    

#####################################################################
# 3.  PRODUCTION DEPLOYMENT (MANUAL TRIGGER)
#####################################################################

release_prod:
  stage: version
  image: node:20-alpine
  tags: [ docker ]
  rules:
    # Manual trigger for production release on master branch
    - if: '$CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "push"'
      when: manual
  variables:
    GIT_DEPTH: "0"                       # full history for semantic-release
  before_script: *semantic_release_setup
  script: |
    # run semantic-release (uses .releaserc.json configuration)
    npx semantic-release
    # fallback when no tags existed yet
    VERSION=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0-${CI_COMMIT_SHORT_SHA}")

    echo "PROD_RELEASE_VERSION=$VERSION" > prod_release.env
  artifacts:
    reports:
      dotenv: prod_release.env

#-------------------------------------------------------------------#

build_prod:
  stage: build
  image: docker:24
  services: [ docker:24-dind ]
  needs: [ release_prod ]
  rules:
    # Automatic build after successful release on master branch
    - if: '$CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "push"'
      when: on_success
  script: |
    if [ -z "$PROD_RELEASE_VERSION" ]; then
      echo "❌  PROD_RELEASE_VERSION missing – release_prod failed?"
      exit 1
    fi

    echo "🔧 Building fresh production image: $PROD_RELEASE_VERSION"
    docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
    docker build -t "$CONTAINER_IMAGE:$PROD_RELEASE_VERSION" .
    docker push "$CONTAINER_IMAGE:$PROD_RELEASE_VERSION"

    echo "IMAGE_TAG=$PROD_RELEASE_VERSION" > build_prod.env
  artifacts:
    reports:
      dotenv: build_prod.env

#-------------------------------------------------------------------#

terraform_plan_prod:
  stage: plan
  image:
    name: hashicorp/terraform:latest
    entrypoint: [ "/bin/sh", "-c" ]
  tags: [ docker ]
  needs: [ build_prod ]
  rules:
    # Manual plan for production after successful build
    - if: '$CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "push"'
      when: manual
  before_script: *terraform_setup
  script: |
    echo "🔍 Planning Terraform changes for production..."
    echo "🔍 Production Image: $CONTAINER_IMAGE:$IMAGE_TAG"

    # Initialize Terraform for production environment
    sh ./terraform/scripts/terraform-init.sh production

    # Plan infrastructure changes
    cd terraform/environments/production
    terraform plan \
      -var="container_image=$CONTAINER_IMAGE:$IMAGE_TAG" \
      -var="gitlab_registry_user=$CI_REGISTRY_USER" \
      -var="gitlab_registry_password=$CI_REGISTRY_PASSWORD" \
      -out=tfplan

    echo "✅ Terraform plan completed for production"
  artifacts:
    paths:
      - terraform/environments/production/tfplan
    expire_in: 1 hour

deploy_prod:
  stage: deploy
  image:
    name: hashicorp/terraform:latest
    entrypoint: [ "/bin/sh", "-c" ]
  tags: [ docker ]
  needs: [ terraform_plan_prod ]
  rules:
    # Manual deployment to production after successful plan
    - if: '$CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "push"'
      when: manual
  environment:
    name: production
    url: https://api.example.com
    action: prepare
  before_script: *terraform_setup
  script: |
    echo "🚀 Deploying $IMAGE_TAG to production environment..."
    echo "🔍 Production Image: $CONTAINER_IMAGE:$IMAGE_TAG"

    # Initialize Terraform for production environment
    sh ./terraform/scripts/terraform-init.sh production

    # Apply infrastructure changes
    cd terraform/environments/production
    terraform apply -auto-approve tfplan

    # Get application URL and deployment info
    APP_URL=$(terraform output -raw application_url)
    DEPLOYED_IMAGE=$(terraform output -raw deployed_image)

    echo "📍 Application URL: $APP_URL"
    echo "🔍 Deployed Image: $DEPLOYED_IMAGE"
    echo "🔑 GitLab Registry Auth: $CI_REGISTRY_USER (password configured)"
    echo "✅ Production deployment completed"

#-------------------------------------------------------------------#
