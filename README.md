# AWS ECS Demo - NestJS Application

A simple NestJS "Hello World" application designed for AWS ECS deployment demonstration. The application provides HTTP endpoints that return the server's process ID and includes a web UI for interaction.

## Features

- **NestJS Framework**: Modern Node.js framework with TypeScript support
- **Process ID Endpoint**: Returns the current server process ID for demo purposes
- **Web UI**: Simple HTML interface to interact with the API
- **Docker Support**: Multi-stage Dockerfile for production deployment
- **GitLab CI/CD**: Automated pipeline with semantic versioning
- **Health Check**: Built-in health check endpoint for container orchestration

## API Endpoints

- `GET /api/hello` - Returns hello message with process ID and timestamp
- `GET /api/process-id` - Returns current process ID and timestamp
- `GET /api/health` - Health check endpoint
- `GET /` - Serves the web UI

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Docker (for containerization)

### Local Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run start:dev
   ```

3. **Access the application:**
   - Web UI: http://localhost:3000
   - API: http://localhost:3000/api/hello

### Production Build

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Start production server:**
   ```bash
   npm run start:prod
   ```

## Docker

### Build Docker Image

```bash
docker build -t aws-ecs-demo .
```

### Run Container

```bash
docker run -p 3000:3000 aws-ecs-demo
```

The application will be available at http://localhost:3000

### Docker Features

- **Multi-stage build** for optimized production image
- **Non-root user** for security
- **Health check** for container orchestration
- **Alpine Linux** for minimal image size

## GitLab CI/CD

The project includes a comprehensive GitLab CI/CD pipeline with Terraform infrastructure automation:

### Stages

1. **Version**: Semantic versioning for development and master branches
2. **Build**: Compiles TypeScript and builds Docker image
3. **Plan**: Terraform infrastructure planning with container image integration
4. **Deploy**: Automated infrastructure deployment to AWS ECS

### Semantic Versioning

- **Development branch**: Creates pre-release versions (e.g., `1.0.0-dev.1`)
- **Master branch**: Creates production releases (e.g., `1.0.0`)

### Infrastructure as Code

The pipeline uses Terraform to manage AWS infrastructure:

- **GitLab Remote State**: Terraform state managed by GitLab
- **Multi-Environment**: Separate configurations for dev and prod
- **Container Integration**: Docker image tags passed to Terraform
- **Automated Deployment**: Infrastructure changes applied automatically
- **Official HashiCorp Image**: Uses `hashicorp/terraform:latest` with pre-installed Terraform
- **Zero Installation Time**: No tool installation needed, Terraform ready immediately

### Required CI/CD Variables

Configure these variables in GitLab CI/CD settings (`Settings > CI/CD > Variables`):

#### 🔑 AWS Credentials (Required for Infrastructure Deployment)

| Variable Name | Description | Example Value | Required | Masked | Protected |
|---------------|-------------|---------------|----------|---------|-----------|
| `AWS_ACCESS_KEY_ID` | AWS access key for Terraform operations | `AKIAIOSFODNN7EXAMPLE` | ✅ Yes | ✅ Yes | ✅ Yes |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key for Terraform operations | `wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY` | ✅ Yes | ✅ Yes | ✅ Yes |
| `AWS_DEFAULT_REGION` | AWS region for resource deployment | `us-east-1` | ⚠️ Optional* | ❌ No | ❌ No |

*If not set, defaults to `us-east-1`

#### 🔧 GitLab Integration (Required for Semantic Release)

| Variable Name | Description | Example Value | Required | Masked | Protected |
|---------------|-------------|---------------|----------|---------|-----------|
| `GL_TOKEN` | GitLab personal access token with `api` scope | `glpat-xxxxxxxxxxxxxxxxxxxx` | ✅ Yes | ✅ Yes | ✅ Yes |

#### 📦 GitLab Registry (Automatically Provided)

These variables are automatically provided by GitLab CI/CD and do not need manual configuration:

| Variable Name | Description | Automatically Provided |
|---------------|-------------|------------------------|
| `CI_REGISTRY_USER` | GitLab registry username | ✅ Yes |
| `CI_REGISTRY_PASSWORD` | GitLab registry password | ✅ Yes |
| `CI_REGISTRY` | GitLab registry URL | ✅ Yes |
| `CI_REGISTRY_IMAGE` | Full image path for this project | ✅ Yes |

#### 🌐 GitLab CI Built-in Variables (Automatically Provided)

These variables are automatically provided by GitLab CI/CD:

| Variable Name | Description | Automatically Provided |
|---------------|-------------|------------------------|
| `CI_PROJECT_ID` | GitLab project ID | ✅ Yes |
| `CI_JOB_TOKEN` | GitLab job token for API access | ✅ Yes |
| `CI_SERVER_URL` | GitLab server URL | ✅ Yes |
| `CI_PROJECT_PATH` | Project path (group/project) | ✅ Yes |
| `CI_COMMIT_REF_NAME` | Branch or tag name | ✅ Yes |

### 📋 Variable Configuration Instructions

#### Step 1: Navigate to GitLab CI/CD Settings
1. Go to your GitLab project
2. Navigate to `Settings > CI/CD`
3. Expand the `Variables` section
4. Click `Add variable`

#### Step 2: Configure AWS Credentials
1. **AWS_ACCESS_KEY_ID**:
   - Key: `AWS_ACCESS_KEY_ID`
   - Value: Your AWS access key
   - ✅ Check "Mask variable"
   - ✅ Check "Protect variable"
   - Click "Add variable"

2. **AWS_SECRET_ACCESS_KEY**:
   - Key: `AWS_SECRET_ACCESS_KEY`
   - Value: Your AWS secret key
   - ✅ Check "Mask variable"
   - ✅ Check "Protect variable"
   - Click "Add variable"

3. **AWS_DEFAULT_REGION** (Optional):
   - Key: `AWS_DEFAULT_REGION`
   - Value: `us-east-1` (or your preferred region)
   - ❌ Leave "Mask variable" unchecked
   - ❌ Leave "Protect variable" unchecked
   - Click "Add variable"

#### Step 3: Configure GitLab Token
1. **Create GitLab Access Token**:
   - Go to `User Settings > Access Tokens`
   - Token name: `CI/CD Pipeline Token`
   - Scopes: ✅ `api`
   - Click "Create personal access token"
   - Copy the generated token

2. **Add GL_TOKEN Variable**:
   - Key: `GL_TOKEN`
   - Value: Your GitLab access token
   - ✅ Check "Mask variable"
   - ✅ Check "Protect variable"
   - Click "Add variable"

### ⚠️ Important Security Notes

- **Protected Variables**: Only available to protected branches (master, main)
- **Masked Variables**: Values are hidden in job logs
- **AWS Credentials**: Use IAM user with minimal required permissions
- **GitLab Token**: Use personal access token, not project access token
- **Token Scope**: GitLab token only needs `api` scope for semantic-release

### 🔐 AWS IAM Permissions

The AWS credentials must have the following permissions for Terraform operations:

#### Required AWS Services
- **ECS**: Create and manage Fargate clusters, services, and task definitions
- **IAM**: Create and manage ECS execution roles and policies
- **EC2**: Access default VPC, subnets, and security groups
- **CloudWatch**: Create and manage log groups
- **Secrets Manager**: Create and manage GitLab registry authentication secrets

#### Minimal IAM Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecs:*",
        "iam:CreateRole",
        "iam:DeleteRole",
        "iam:AttachRolePolicy",
        "iam:DetachRolePolicy",
        "iam:PutRolePolicy",
        "iam:DeleteRolePolicy",
        "iam:GetRole",
        "iam:PassRole",
        "ec2:DescribeVpcs",
        "ec2:DescribeSubnets",
        "ec2:DescribeSecurityGroups",
        "ec2:CreateSecurityGroup",
        "ec2:DeleteSecurityGroup",
        "ec2:AuthorizeSecurityGroupIngress",
        "ec2:AuthorizeSecurityGroupEgress",
        "ec2:RevokeSecurityGroupIngress",
        "ec2:RevokeSecurityGroupEgress",
        "logs:CreateLogGroup",
        "logs:DeleteLogGroup",
        "logs:DescribeLogGroups",
        "logs:PutRetentionPolicy",
        "secretsmanager:CreateSecret",
        "secretsmanager:DeleteSecret",
        "secretsmanager:UpdateSecret",
        "secretsmanager:GetSecretValue",
        "secretsmanager:PutSecretValue",
        "secretsmanager:DescribeSecret"
      ],
      "Resource": "*"
    }
  ]
}
```

#### Creating AWS IAM User
1. Go to AWS IAM Console
2. Create new user: `terraform-ci-cd`
3. Attach the policy above
4. Generate access keys
5. Use the access keys in GitLab CI/CD variables

### 🔍 Variable Validation

The GitLab CI/CD pipeline includes comprehensive validation for all required variables:

#### Automatic Validation Checks
- ✅ **AWS Credentials**: Validates `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` are set
- ✅ **AWS Region**: Uses `us-east-1` default if `AWS_DEFAULT_REGION` not specified
- ✅ **GitLab Registry**: Validates `CI_REGISTRY_USER` and `CI_REGISTRY_PASSWORD` are available
- ✅ **GitLab CI Variables**: Validates `CI_PROJECT_ID`, `CI_JOB_TOKEN`, and `CI_SERVER_URL`
- ✅ **Semantic Release**: Validates `GL_TOKEN` for version management jobs

#### Error Messages
If any required variable is missing, the pipeline will fail with clear error messages:
```
❌ Error: AWS credentials not configured
   Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in GitLab CI/CD variables
   Go to: Settings > CI/CD > Variables
```

#### Troubleshooting Variable Issues
1. **Check GitLab CI/CD Variables**: Go to `Settings > CI/CD > Variables`
2. **Verify Variable Names**: Ensure exact spelling and case sensitivity
3. **Check Variable Scope**: Ensure protected variables are available to your branch
4. **Review Job Logs**: Look for validation error messages in pipeline logs

### 🔧 Terraform State Authentication Fix

#### Issue: "HTTP remote state endpoint requires auth" during deploy
This error occurs when the `deploy_feature_to_dev` job fails with state lock authentication issues, even though the `terraform_plan_feature` job succeeds.

**Root Cause**: When using `-backend-config=password=${CI_JOB_TOKEN}` during `terraform plan`, the token gets embedded in the plan file. Since `CI_JOB_TOKEN` is only valid for the current job, the subsequent deploy job cannot authenticate with the cached token.

**Solution**: The pipeline now uses environment variables (`TF_HTTP_USERNAME` and `TF_HTTP_PASSWORD`) instead of backend configuration parameters, preventing token persistence in plan files. This follows the [official GitLab documentation](https://docs.gitlab.com/user/infrastructure/iac/terraform_state/#customizing-your-opentofu-environment-variables) recommendation.

**How it works**:
1. Both plan and apply phases use `CI_JOB_TOKEN` via environment variables
2. No authentication tokens are embedded in plan files
3. Each job uses its own valid `CI_JOB_TOKEN` for state operations

**Verification**:
- ✅ Plan job shows: "🔑 Using CI_JOB_TOKEN for authentication"
- ✅ Deploy job shows: "🔑 Using CI_JOB_TOKEN for authentication"
- ✅ No "HTTP remote state endpoint requires auth" errors

### 🔧 CI/CD Pipeline Architecture

#### Docker Image Solution
The pipeline uses **`hashicorp/terraform:latest`** (official HashiCorp image) with Terraform pre-installed and AWS authentication via environment variables.

**Image Details:**
- **Base Image**: `hashicorp/terraform:latest` (official HashiCorp image)
- **Base OS**: Alpine Linux v3.22 (lightweight and secure)
- **Terraform**: v1.12.2 (latest, pre-installed and ready to use)
- **AWS Authentication**: Via environment variables (no AWS CLI installation needed)
- **Setup Time**: Instant - no tool installation required
- **User Context**: Root (no permission issues in GitLab CI)
- **Maintenance**: Official HashiCorp image, regularly updated

**Benefits:**
- ✅ **Latest Terraform version** - Always up-to-date with official releases
- ✅ **Zero installation time** - Terraform is pre-installed and ready
- ✅ **Official and trusted** - HashiCorp's official Docker image
- ✅ **Lightweight** - Alpine Linux base keeps image size minimal
- ✅ **Simple and reliable** - No complex installation or dependency management
- ✅ **Direct AWS integration** - Terraform authenticates with AWS using environment variables

## Scripts

- `npm run build` - Build the application
- `npm run start` - Start the application
- `npm run start:dev` - Start in development mode with hot reload
- `npm run start:prod` - Start in production mode
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## Project Structure

```
├── src/
│   ├── app.controller.ts    # API endpoints
│   ├── app.service.ts       # Business logic
│   ├── app.module.ts        # Application module
│   └── main.ts              # Application entry point
├── public/
│   └── index.html           # Web UI
├── terraform/               # Infrastructure as Code
│   ├── main.tf             # Main Terraform configuration
│   ├── variables.tf        # Input variables
│   ├── outputs.tf          # Output values
│   ├── versions.tf         # Provider versions
│   ├── environments/       # Environment-specific configs
│   │   ├── dev/           # Development environment
│   │   └── prod/          # Production environment
│   ├── scripts/           # Helper scripts
│   └── README.md          # Terraform documentation
├── Dockerfile               # Docker configuration
├── .gitlab-ci.yml          # GitLab CI/CD pipeline
├── .releaserc.json         # Semantic release configuration
└── package.json            # Dependencies and scripts
```

## Environment Variables

- `PORT` - Server port (default: 3000)

## Health Check

The application includes a health check endpoint at `/api/health` that returns:

```json
{
  "status": "OK",
  "processId": 12345,
  "timestamp": "2023-12-07T10:30:00.000Z"
}
```

## AWS ECS Deployment

This application is designed for AWS ECS deployment with complete infrastructure automation:

### Infrastructure Features
- **Default VPC**: Uses AWS default networking for simplicity
- **ECS Fargate**: Serverless container orchestration
- **Security Groups**: Basic HTTP access controls
- **CloudWatch Logs**: Centralized logging with 3-day retention
- **GitLab Registry Auth**: Secure private registry access via AWS Secrets Manager
- **Public IP Access**: Direct container access for demo purposes

### Container Features
- Non-root user for security
- Optimized multi-stage build
- Port 3000 exposed for direct access
- CloudWatch logging integration
- GitLab registry authentication

### Deployment Process
1. **Build**: Docker image built and pushed to GitLab registry
2. **Plan**: Terraform plans infrastructure changes with GitLab auth
3. **Deploy**: Infrastructure deployed with new container image
4. **Verify**: Check ECS console for task public IP and access application

### Environment Access
- **Development**: Automatic deployment from `development` branch
- **Production**: Manual deployment from `master` branch
- **Feature Testing**: Manual deployment for feature branches
- **Access Method**: Direct via ECS task public IP (check AWS ECS console)

## Contributing

1. Create a feature branch from `development`
2. Make your changes
3. Ensure tests pass and code is formatted
4. Create a merge request to `development`
5. After review, merge to `development` for testing
6. Create a merge request from `development` to `master` for production release

## License

MIT
